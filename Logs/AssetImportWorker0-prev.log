Unity Editor version:    6000.2.0f1 (eed1c594c913)
Branch:                  6000.2/respin/6000.2.0f1-517f89d850d1
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.7 (Build 24G222)
Darwin version:          24.6.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        65536 MB
Date:                    2025-09-24T09:45:31Z
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cat" at "2025-09-24T09:45:31.433918Z"

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Documents/MyProject
-logFile
Logs/AssetImportWorker0.log
-srvPort
49912
-licensingIpc
LicenseClient-cat
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name AssetImport
Successfully changed project path to: /Users/<USER>/Documents/MyProject
/Users/<USER>/Documents/MyProject
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8413602112]  Target information:

Player connection [8413602112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1684640938 [EditorId] 1684640938 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8413602112]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1684640938 [EditorId] 1684640938 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8413602112] Host joined multi-casting on [***********:54997]...
Player connection [8413602112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.47 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 2.71 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/MyProject/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M3 Max (high power)
Metal devices available: 1
0: Apple M3 Max (high power)
Using device Apple M3 Max (high power)
Initializing Metal device caps: Apple M3 Max
Mac GPU Family: 2
Apple GPU Family: 9
MSL Version: 3.0
Has Float MSAA: 1
Has Float Filtering: 1
ReadWrite Texture Tier: 2
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 2276, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              a5fb485c3f154643b1062372f3aee58a
  Correlation Id:          6fb624e12d3adbe0059179404fe07691
  External correlation Id: 649091515936642959
  Machine Id:              grnFDnHWQSWOHO69FTKqhp+4KtY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-cat" (connect: 0.00s, validation: 0.00s, handshake: 0.35s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cat-notifications" at "2025-09-24T09:45:31.788767Z"
[Licensing::Module] Licensing Background thread has ended after 0.35s
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56583
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: 1375785438378-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.001529 seconds.
- Loaded All Assemblies, in  0.218 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 72 ms
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.351 seconds
Domain Reload Profiling: 569ms
	BeginReloadAssembly (45ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (112ms)
		LoadAssemblies (45ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (110ms)
			TypeCache.Refresh (109ms)
				TypeCache.ScanAssembly (101ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (351ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (319ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (170ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (41ms)
			ProcessInitializeOnLoadAttributes (69ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.570 seconds
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.575 seconds
Domain Reload Profiling: 1146ms
	BeginReloadAssembly (83ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (433ms)
		LoadAssemblies (192ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (271ms)
			TypeCache.Refresh (228ms)
				TypeCache.ScanAssembly (212ms)
			BuildScriptInfoCaches (34ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (576ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (459ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (212ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launching external process: /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.70 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.49 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8655 unused Assets / (16.7 MB). Loaded Objects now: 9382.
Memory consumption went from 184.5 MB to 167.7 MB.
Total: 8.608917 ms (FindLiveObjects: 0.799542 ms CreateObjectMapping: 0.189625 ms MarkObjects: 4.902167 ms  DeleteObjects: 2.717291 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x3522bf000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.559 seconds
Refreshing native plugins compatible for Editor in 0.49 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.612 seconds
Domain Reload Profiling: 1172ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (15ms)
	LoadAllAssembliesAndSetupDomain (376ms)
		LoadAssemblies (207ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (197ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (612ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (497ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (169ms)
			ProcessInitializeOnLoadAttributes (220ms)
			ProcessInitializeOnLoadMethodAttributes (91ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.52 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.40 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8653 unused Assets / (16.7 MB). Loaded Objects now: 9398.
Memory consumption went from 200.8 MB to 184.1 MB.
Total: 7.299042 ms (FindLiveObjects: 0.390125 ms CreateObjectMapping: 0.183833 ms MarkObjects: 4.736375 ms  DeleteObjects: 1.988250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.45 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.38 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8638 unused Assets / (16.1 MB). Loaded Objects now: 9398.
Memory consumption went from 188.7 MB to 172.6 MB.
Total: 7.506667 ms (FindLiveObjects: 0.401208 ms CreateObjectMapping: 0.271417 ms MarkObjects: 4.831916 ms  DeleteObjects: 2.001708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 18139.575354 seconds.
  path: Assets/InputSystem/PlayerInputs.inputactions
  artifactKey: Guid(aa7d4a4d5e05d4a8294dc27b92e64992) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem/PlayerInputs.inputactions using Guid(aa7d4a4d5e05d4a8294dc27b92e64992) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '853dfc6276d3809e461617b5fe9d3312') in 0.015419042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x350ec7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.503 seconds
Refreshing native plugins compatible for Editor in 0.43 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.597 seconds
Domain Reload Profiling: 1102ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (340ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (597ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (216ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.45 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8653 unused Assets / (16.0 MB). Loaded Objects now: 9404.
Memory consumption went from 201.2 MB to 185.1 MB.
Total: 7.783167 ms (FindLiveObjects: 0.386667 ms CreateObjectMapping: 0.188125 ms MarkObjects: 5.216000 ms  DeleteObjects: 1.992083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.46 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8638 unused Assets / (15.8 MB). Loaded Objects now: 9404.
Memory consumption went from 189.2 MB to 173.4 MB.
Total: 7.313250 ms (FindLiveObjects: 0.403250 ms CreateObjectMapping: 0.273333 ms MarkObjects: 4.706750 ms  DeleteObjects: 1.929542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 52.812802 seconds.
  path: Assets/InputSystem/PlayerInputs.inputactions
  artifactKey: Guid(aa7d4a4d5e05d4a8294dc27b92e64992) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem/PlayerInputs.inputactions using Guid(aa7d4a4d5e05d4a8294dc27b92e64992) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e1b9ae1e8082421e7d1da131ccf6d59') in 0.014083416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x3522bf000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.512 seconds
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.607 seconds
Domain Reload Profiling: 1122ms
	BeginReloadAssembly (123ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (347ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (607ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (173ms)
			ProcessInitializeOnLoadAttributes (220ms)
			ProcessInitializeOnLoadMethodAttributes (88ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.54 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.49 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8653 unused Assets / (16.1 MB). Loaded Objects now: 9407.
Memory consumption went from 201.0 MB to 184.9 MB.
Total: 7.624291 ms (FindLiveObjects: 0.426709 ms CreateObjectMapping: 0.231083 ms MarkObjects: 4.933292 ms  DeleteObjects: 2.032791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.51 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.47 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8638 unused Assets / (15.9 MB). Loaded Objects now: 9407.
Memory consumption went from 189.3 MB to 173.3 MB.
Total: 7.416541 ms (FindLiveObjects: 0.397333 ms CreateObjectMapping: 0.177792 ms MarkObjects: 4.812583 ms  DeleteObjects: 2.028333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x3522bf000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.509 seconds
Refreshing native plugins compatible for Editor in 0.46 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.620 seconds
Domain Reload Profiling: 1132ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (348ms)
		LoadAssemblies (186ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (187ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (621ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (515ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (173ms)
			ProcessInitializeOnLoadAttributes (228ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.54 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.59 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8653 unused Assets / (16.1 MB). Loaded Objects now: 9410.
Memory consumption went from 200.7 MB to 184.6 MB.
Total: 7.578125 ms (FindLiveObjects: 0.381125 ms CreateObjectMapping: 0.201708 ms MarkObjects: 4.932500 ms  DeleteObjects: 2.062458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.47 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.42 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8639 unused Assets / (16.1 MB). Loaded Objects now: 9411.
Memory consumption went from 189.3 MB to 173.2 MB.
Total: 7.213542 ms (FindLiveObjects: 0.391333 ms CreateObjectMapping: 0.278875 ms MarkObjects: 4.564833 ms  DeleteObjects: 1.978417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 968.049408 seconds.
  path: Assets/Scripts/APILearn/API11Camera/API11ScreenPointToRay.cs
  artifactKey: Guid(de3e6e6b2fc4a42d2af371057f0c1290) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/APILearn/API11Camera/API11ScreenPointToRay.cs using Guid(de3e6e6b2fc4a42d2af371057f0c1290) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '408097c4484d3a7c4085352dbe36b862') in 0.000980042 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x3522bf000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.505 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.593 seconds
Domain Reload Profiling: 1104ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (347ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (189ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (593ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (489ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (213ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.54 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.57 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8654 unused Assets / (16.2 MB). Loaded Objects now: 9414.
Memory consumption went from 200.9 MB to 184.7 MB.
Total: 7.896792 ms (FindLiveObjects: 0.405209 ms CreateObjectMapping: 0.192000 ms MarkObjects: 5.005500 ms  DeleteObjects: 2.293666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 7.112631 seconds.
  path: Assets/Scripts/APILearn/API11Camera/API11ScreenPointToRay.cs
  artifactKey: Guid(de3e6e6b2fc4a42d2af371057f0c1290) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/APILearn/API11Camera/API11ScreenPointToRay.cs using Guid(de3e6e6b2fc4a42d2af371057f0c1290) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '341be6de4e29fddb1bae3d7b0f8f611f') in 0.000955625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x3522bf000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.511 seconds
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.612 seconds
Domain Reload Profiling: 1126ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (350ms)
		LoadAssemblies (190ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (612ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (501ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (172ms)
			ProcessInitializeOnLoadAttributes (220ms)
			ProcessInitializeOnLoadMethodAttributes (92ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.56 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.57 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8654 unused Assets / (16.3 MB). Loaded Objects now: 9417.
Memory consumption went from 200.9 MB to 184.6 MB.
Total: 8.250500 ms (FindLiveObjects: 0.406250 ms CreateObjectMapping: 0.178875 ms MarkObjects: 5.372708 ms  DeleteObjects: 2.292209 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16ff37000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.517 seconds
Refreshing native plugins compatible for Editor in 0.43 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.598 seconds
Domain Reload Profiling: 1118ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (353ms)
		LoadAssemblies (189ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (599ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (171ms)
			ProcessInitializeOnLoadAttributes (219ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.54 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8654 unused Assets / (15.8 MB). Loaded Objects now: 9420.
Memory consumption went from 200.6 MB to 184.8 MB.
Total: 7.976250 ms (FindLiveObjects: 0.451958 ms CreateObjectMapping: 0.192625 ms MarkObjects: 5.036250 ms  DeleteObjects: 2.295125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x350ec7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.508 seconds
Refreshing native plugins compatible for Editor in 0.45 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.606 seconds
Domain Reload Profiling: 1117ms
	BeginReloadAssembly (122ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (344ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (606ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (172ms)
			ProcessInitializeOnLoadAttributes (220ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.47 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8654 unused Assets / (15.6 MB). Loaded Objects now: 9423.
Memory consumption went from 200.7 MB to 185.1 MB.
Total: 8.157041 ms (FindLiveObjects: 0.464417 ms CreateObjectMapping: 0.211875 ms MarkObjects: 5.103791 ms  DeleteObjects: 2.371541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.44 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8639 unused Assets / (16.1 MB). Loaded Objects now: 9423.
Memory consumption went from 189.6 MB to 173.5 MB.
Total: 7.495416 ms (FindLiveObjects: 0.399584 ms CreateObjectMapping: 0.198666 ms MarkObjects: 4.742167 ms  DeleteObjects: 2.154583 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
