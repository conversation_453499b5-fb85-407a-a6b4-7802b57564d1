Unity Editor version:    6000.2.0f1 (eed1c594c913)
Branch:                  6000.2/respin/6000.2.0f1-517f89d850d1
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.6.1 (Build 24G90)
Darwin version:          24.6.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        65536 MB
Date:                    2025-09-22T08:29:18Z
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cat" at "2025-09-22T08:29:18.381756Z"

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Documents/MyProject
-logFile
Logs/AssetImportWorker0.log
-srvPort
51937
-licensingIpc
LicenseClient-cat
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name AssetImport
Successfully changed project path to: /Users/<USER>/Documents/MyProject
/Users/<USER>/Documents/MyProject
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8473616576]  Target information:

Player connection [8473616576]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 55419825 [EditorId] 55419825 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8473616576]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 55419825 [EditorId] 55419825 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8473616576] Host joined multi-casting on [***********:54997]...
Player connection [8473616576] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 2.43 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/MyProject/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M3 Max (high power)
Metal devices available: 1
0: Apple M3 Max (high power)
Using device Apple M3 Max (high power)
Initializing Metal device caps: Apple M3 Max
Mac GPU Family: 2
Apple GPU Family: 9
MSL Version: 3.0
Has Float MSAA: 1
Has Float Filtering: 1
ReadWrite Texture Tier: 2
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 8407, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              6ceb7bc3d13543e79823b71a8921f69a
  Correlation Id:          059a60bd38966909ae243d4002812ce5
  External correlation Id: 6450407555913977138
  Machine Id:              grnFDnHWQSWOHO69FTKqhp+4KtY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-cat" (connect: 0.00s, validation: 0.00s, handshake: 0.35s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cat-notifications" at "2025-09-22T08:29:18.735892Z"
[Licensing::Module] Licensing Background thread has ended after 0.35s
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56728
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: 1375785438378-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.002462 seconds.
- Loaded All Assemblies, in  0.213 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 69 ms
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.380 seconds
Domain Reload Profiling: 593ms
	BeginReloadAssembly (44ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (109ms)
		LoadAssemblies (44ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (106ms)
				TypeCache.ScanAssembly (98ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (380ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (347ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (179ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (50ms)
			ProcessInitializeOnLoadAttributes (77ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.586 seconds
Refreshing native plugins compatible for Editor in 0.47 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.598 seconds
Domain Reload Profiling: 1184ms
	BeginReloadAssembly (88ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (443ms)
		LoadAssemblies (200ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (275ms)
			TypeCache.Refresh (231ms)
				TypeCache.ScanAssembly (215ms)
			BuildScriptInfoCaches (33ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (598ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (481ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (16ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (140ms)
			ProcessInitializeOnLoadAttributes (224ms)
			ProcessInitializeOnLoadMethodAttributes (95ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launching external process: /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.64 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.47 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8654 unused Assets / (18.3 MB). Loaded Objects now: 9381.
Memory consumption went from 182.7 MB to 164.4 MB.
Total: 8.490625 ms (FindLiveObjects: 0.412000 ms CreateObjectMapping: 0.181458 ms MarkObjects: 4.841167 ms  DeleteObjects: 3.055541 ms)

========================================================================
Received Import Request.
  Time since last request: 2907.403195 seconds.
  path: Assets/Scripts/APIInputSystem/IPS01InputActionAsset.cs
  artifactKey: Guid(f140cc361ed8942278c826aaa60be22a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/APIInputSystem/IPS01InputActionAsset.cs using Guid(f140cc361ed8942278c826aaa60be22a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '16e7fb421fe90bd22472564a3e66b4f1') in 0.002096958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Scripts/APIInputSystem/IPS06InputCustom.cs
  artifactKey: Guid(0762ee435bf66444a909889814ddf6b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/APIInputSystem/IPS06InputCustom.cs using Guid(0762ee435bf66444a909889814ddf6b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ec7452b123e88704221a4241ead78b5') in 0.000270167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16db57000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.525 seconds
Refreshing native plugins compatible for Editor in 0.40 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.604 seconds
Domain Reload Profiling: 1131ms
	BeginReloadAssembly (122ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (204ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (604ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (170ms)
			ProcessInitializeOnLoadAttributes (214ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.49 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.51 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8652 unused Assets / (16.8 MB). Loaded Objects now: 9399.
Memory consumption went from 199.4 MB to 182.6 MB.
Total: 7.673000 ms (FindLiveObjects: 0.401750 ms CreateObjectMapping: 0.190542 ms MarkObjects: 4.995083 ms  DeleteObjects: 2.085208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x36c7df000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.526 seconds
Refreshing native plugins compatible for Editor in 0.37 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.528 seconds
Domain Reload Profiling: 1055ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (365ms)
		LoadAssemblies (207ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (528ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (436ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (187ms)
			ProcessInitializeOnLoadMethodAttributes (83ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.65 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.42 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8652 unused Assets / (16.1 MB). Loaded Objects now: 9402.
Memory consumption went from 201.1 MB to 185.0 MB.
Total: 6.976666 ms (FindLiveObjects: 0.376458 ms CreateObjectMapping: 0.198167 ms MarkObjects: 4.332083 ms  DeleteObjects: 2.069791 ms)

Prepare: number of updated asset objects reloaded= 0
