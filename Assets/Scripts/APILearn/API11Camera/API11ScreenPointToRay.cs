using UnityEngine;
using UnityEngine.InputSystem;

public class API11ScreenPointToRay : MonoBehaviour
{
  private PlayerInputs myInput;
  public Camera myCamera;

  void Awake()
  {
    myInput ??= new PlayerInputs();
  }

  void OnEnable()
  {
    myInput.Enable();
    myInput.Mouse.Click.performed += OnClick;
  }

  void OnDisable()
  {
    myInput.Mouse.Click.performed -= OnClick;
    myInput.Disable();
  }

  void OnClick(InputAction.CallbackContext context)
  {
    Vector3 mousePosition = Mouse.current.position.ReadValue();
    Ray ray = myCamera.ScreenPointToRay(mousePosition);

    Debug.DrawRay(ray.origin, ray.direction * 100, Color.red, 2.0f);
  }
}
