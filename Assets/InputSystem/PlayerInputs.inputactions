{"version": 1, "name": "PlayerInputs", "maps": [{"name": "Player", "id": "a8d297d3-9d55-4c8e-a8bd-74bc75862222", "actions": [{"name": "Move", "type": "Value", "id": "52a28d12-1c19-4afb-8513-b77e42de280b", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "WASD", "id": "08780ffa-eb43-4d23-8431-2b0cb66fda55", "path": "Dpad", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "dea8b7bd-9603-45b0-ac56-20106ff18732", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "6c308549-e672-4dfa-aeb8-b74b01a4d596", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "00c2554b-65c4-4f31-9875-953544d0907c", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "9a0fe63b-7936-4def-a3a1-b86154363c19", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}]}, {"name": "Mouse", "id": "cc66715b-a996-4b71-bbd9-9ed1cf8e2cc5", "actions": [{"name": "Click", "type": "Value", "id": "71f045dd-33b1-43e2-b617-1dba5abb4e31", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "", "id": "01250166-6671-4071-900f-60f282150122", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "", "action": "Click", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "Touch", "bindingGroup": "Touch", "devices": [{"devicePath": "<Touchscreen>", "isOptional": false, "isOR": false}]}, {"name": "Joystick", "bindingGroup": "Joystick", "devices": [{"devicePath": "<Joystick>", "isOptional": false, "isOR": false}]}, {"name": "XR", "bindingGroup": "XR", "devices": [{"devicePath": "<XRController>", "isOptional": false, "isOR": false}]}]}